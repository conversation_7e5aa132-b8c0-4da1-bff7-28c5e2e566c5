{"name": "doviz-app", "license": "0BSD", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "start:dev": "NODE_ENV=development expo start", "start:prod": "NODE_ENV=production expo start", "android": "expo start --android", "android:dev": "NODE_ENV=development expo start --android", "android:prod": "NODE_ENV=production expo start --android", "ios": "expo start --ios", "ios:dev": "NODE_ENV=development expo start --ios", "ios:prod": "NODE_ENV=production expo start --ios", "web": "expo start --web", "web:dev": "NODE_ENV=development expo start --web", "web:prod": "NODE_ENV=production expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/slider": "^4.5.6", "axios": "^1.9.0", "expo": "~53.0.9", "expo-blur": "^14.1.4", "expo-constants": "^17.1.7", "expo-linear-gradient": "^14.1.4", "expo-status-bar": "~2.2.3", "moment": "^2.30.1", "moti": "^0.30.0", "react": "19.0.0", "react-native": "0.79.2", "react-native-chart-kit": "^6.12.0", "react-native-dropdown-picker": "^5.4.6", "react-native-paper": "^5.14.5", "react-native-paper-dropdown": "^2.3.1", "react-native-reanimated": "^3.17.4", "react-native-svg": "^15.11.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/moment": "^2.13.0", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}